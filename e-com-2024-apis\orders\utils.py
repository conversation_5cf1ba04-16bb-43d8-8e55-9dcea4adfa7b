import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from datetime import datetime

logger = logging.getLogger(__name__)

def send_email(subject, template_name, context, to_email):
    """
    Helper function to send an email.

    Args:
        subject: Email subject
        template_name: Template file name
        context: Template context
        to_email: Recipient email address or list of addresses

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        from_email = settings.DEFAULT_FROM_EMAIL

        # Render HTML content
        html_content = render_to_string(template_name, context)

        # Convert single email to list if needed
        recipients = to_email if isinstance(to_email, list) else [to_email]

        # Create email message
        email = EmailMultiAlternatives(subject, "", from_email, recipients)
        email.attach_alternative(html_content, "text/html")

        # Send email
        email.send()
        return True
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def send_order_confirmation_email(order, payment=None):
    """
    Send order confirmation emails to both the customer and the admin.

    Args:
        order: Order object with all related information
        payment: Optional Payment object with transaction details

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    success = True
    # Determine payment method
    payment_method = "PhonePe"  # Default for now

    # Calculate GST breakdown for each item
    item_gst_details = []
    for item in order.items.all():
        # Calculate GST breakdown for this item
        gst_breakdown = item.product.calculate_gst_breakdown_from_mrp(quantity=item.quantity)
        item_gst_details.append({
            'item': item,
            'base_price': gst_breakdown['unit_base_price'],
            'total_base_price': gst_breakdown['total_base_price'],
            'gst_rate': gst_breakdown['gst_rate'],
            'gst_amount': gst_breakdown['total_gst'],
            'cgst_amount': gst_breakdown['cgst_amount'],
            'sgst_amount': gst_breakdown['sgst_amount'],
            'igst_amount': gst_breakdown['igst_amount'],
            'total_with_gst': gst_breakdown['total_mrp']
        })

    # Context for templates
    context = {
        'order': order,
        'payment_method': payment_method,
        'item_gst_details': item_gst_details
    }

    # Add payment status if payment is provided
    if payment:
        context['payment_status'] = payment.status

    # Send email to customer
    customer_subject = f"Order Confirmation - Triumph Enterprises #{order.id}"
    customer_result = send_email(
        subject=customer_subject,
        template_name='emails/order_confirmation.html',
        context=context,
        to_email=order.user.email
    )

    if customer_result:
        logger.info(f"Order confirmation email sent to customer for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send order confirmation email to customer for order {order.id}")

    # Send notification to admin and Triumph Enterprises email
    admin_subject = f"New Order Notification - Order #{order.id}"
    admin_result = send_email(
        subject=admin_subject,
        template_name='emails/admin_order_notification.html',
        context=context,
        to_email=[settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
    )

    if admin_result:
        logger.info(f"Order notification email sent to admin for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send order notification email to admin for order {order.id}")

    return success

def send_payment_success_email(order, payment):
    """
    Send payment success emails to both the customer and the admin.

    Args:
        order: Order object
        payment: Payment object with transaction details

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    success = True

    # Context for templates
    context = {
        'order': order,
        'payment': payment
    }

    # Send email to customer
    customer_subject = f"Payment Successful - Triumph Enterprises Order #{order.id}"
    customer_result = send_email(
        subject=customer_subject,
        template_name='emails/payment_success.html',
        context=context,
        to_email=order.user.email
    )

    if customer_result:
        logger.info(f"Payment success email sent to customer for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send payment success email to customer for order {order.id}")

    # Send notification to admin and Triumph Enterprises email
    admin_subject = f"Payment Success Notification - Order #{order.id}"
    admin_result = send_email(
        subject=admin_subject,
        template_name='emails/admin_payment_notification.html',
        context=context,
        to_email=[settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
    )

    if admin_result:
        logger.info(f"Payment success notification email sent to admin for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send payment success notification email to admin for order {order.id}")

    return success

def send_payment_failure_email(order, payment):
    """
    Send payment failure emails to both the customer and the admin.

    Args:
        order: Order object
        payment: Payment object with transaction details

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    success = True

    # Context for templates
    context = {
        'order': order,
        'payment': payment,
        'frontend_url': settings.FRONTEND_URL
    }

    # Send email to customer
    customer_subject = f"Payment Failed - Triumph Enterprises Order #{order.id}"
    customer_result = send_email(
        subject=customer_subject,
        template_name='emails/payment_failure.html',
        context=context,
        to_email=order.user.email
    )

    if customer_result:
        logger.info(f"Payment failure email sent to customer for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send payment failure email to customer for order {order.id}")

    # Send notification to admin and Triumph Enterprises email
    admin_subject = f"Payment Failure Notification - Order #{order.id}"
    admin_result = send_email(
        subject=admin_subject,
        template_name='emails/admin_payment_notification.html',
        context=context,
        to_email=[settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]
    )

    if admin_result:
        logger.info(f"Payment failure notification email sent to admin for order {order.id}")
    else:
        success = False
        logger.error(f"Failed to send payment failure notification email to admin for order {order.id}")

    return success
