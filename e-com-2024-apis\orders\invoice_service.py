"""
Invoice generation service for GST-compliant invoices
"""
import os
from datetime import datetime
from decimal import Decimal
from io import BytesIO
from django.conf import settings
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.platypus.flowables import HRFlowable
from .models import Order, Invoice


class InvoiceGenerationService:
    """Service for generating GST-compliant PDF invoices"""

    def __init__(self):
        self.company_details = {
            'name': 'Triumph Enterprises',
            'address': 'D.No. 5-5-190/65A, <PERSON><PERSON><PERSON>,\nPatel Nagar, Darussalam, Hyderabad, Telangana 500001, India',
            'email': '<EMAIL>',
            'phone': '+91 9848486452',
            'gst_number': '36AANFT7502P1ZH',  # Add actual GST number
            'website': 'https://trio.net.in'
        }

    def generate_invoice(self, order: Order) -> Invoice:
        """
        Generate a GST-compliant invoice for an order

        Note: Invoices should only be generated for paid orders as per business requirements.

        Args:
            order: Order instance

        Returns:
            Invoice instance with generated PDF

        Raises:
            ValueError: If order is not in PAID status
        """
        # Check if order is paid before generating invoice
        if order.status != 'PAID':
            raise ValueError(f"Invoice can only be generated for paid orders. Current order status: {order.status}")

        # Create or get existing invoice
        invoice, created = Invoice.objects.get_or_create(
            order=order,
            defaults={
                'company_name': self.company_details['name'],
                'company_address': self.company_details['address'],
                'company_email': self.company_details['email'],
                'company_phone': self.company_details['phone'],
                'gst_number': self.company_details['gst_number']
            }
        )

        # Generate PDF if not exists or if recreating
        if created or not invoice.pdf_file:
            pdf_content = self._generate_pdf(order, invoice)

            # Save PDF file
            filename = f"invoice_{invoice.invoice_number}.pdf"
            invoice.pdf_file.save(
                filename,
                ContentFile(pdf_content),
                save=True
            )

        return invoice

    def _generate_pdf(self, order: Order, invoice: Invoice) -> bytes:
        """Generate PDF content for the invoice"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            topMargin=0.8*inch,
            bottomMargin=0.8*inch,
            leftMargin=0.8*inch,
            rightMargin=0.8*inch
        )

        # Define custom styles
        styles = getSampleStyleSheet()

        # Company name style
        company_style = ParagraphStyle(
            'CompanyStyle',
            parent=styles['Heading1'],
            fontSize=28,
            spaceAfter=8,
            textColor=colors.HexColor('#1a365d'),
            alignment=1,  # Center
            fontName='Helvetica-Bold'
        )

        # Invoice title style
        invoice_title_style = ParagraphStyle(
            'InvoiceTitle',
            parent=styles['Heading2'],
            fontSize=20,
            spaceAfter=20,
            textColor=colors.HexColor('#2d3748'),
            alignment=1,  # Center
            fontName='Helvetica-Bold'
        )

        # Section heading style
        section_heading_style = ParagraphStyle(
            'SectionHeading',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=16,
            textColor=colors.HexColor('#2d3748'),
            fontName='Helvetica-Bold'
        )

        # Normal text style
        normal_style = ParagraphStyle(
            'NormalStyle',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=4,
            textColor=colors.HexColor('#4a5568')
        )

        # Small text style
        small_style = ParagraphStyle(
            'SmallStyle',
            parent=styles['Normal'],
            fontSize=9,
            spaceAfter=2,
            textColor=colors.HexColor('#718096')
        )

        # Build PDF content
        story = []

        # Header with company info and invoice title
        header_data = [
            [
                Paragraph(f'<b>{self.company_details["name"]}</b>', company_style),
                Paragraph('<b>TAX INVOICE</b>', invoice_title_style)
            ]
        ]
        header_table = Table(header_data, colWidths=[4*inch, 2.5*inch])
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ]))
        story.append(header_table)
        story.append(Spacer(1, 10))

        # Company details and invoice info
        company_invoice_data = [
            [
                Paragraph(f'{self.company_details["address"]}<br/>Email: {self.company_details["email"]}<br/>Phone: {self.company_details["phone"]}<br/>GST: {self.company_details["gst_number"]}', normal_style),
                Paragraph(f'<b>Invoice No:</b> {invoice.invoice_number}<br/><b>Invoice Date:</b> {invoice.generated_at.strftime("%d %b %Y")}<br/><b>Order No:</b> {str(order.id)[:8]}...<br/><b>Order Date:</b> {order.created_at.strftime("%d %b %Y")}', normal_style)
            ]
        ]
        company_invoice_table = Table(company_invoice_data, colWidths=[4*inch, 2.5*inch])
        company_invoice_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
        ]))
        story.append(company_invoice_table)
        story.append(Spacer(1, 20))

        # Billing address section
        story.append(Paragraph("BILLING ADDRESS", section_heading_style))
        billing_info = f"""
        <b>{order.user.get_full_name() or order.user.email}</b><br/>
        {order.billing_address.street_address}<br/>
        {order.billing_address.city}, {order.billing_address.state} - {order.billing_address.postal_code}<br/>
        {order.billing_address.country}<br/>
        Email: {order.user.email}
        """
        story.append(Paragraph(billing_info, normal_style))
        story.append(Spacer(1, 20))

        # Order Items Section
        story.append(Paragraph("ORDER DETAILS", section_heading_style))
        story.append(Spacer(1, 10))

        # Items table with modern design
        item_data = []

        # Header row
        item_data.append([
            Paragraph('<b>PRODUCT DETAILS</b>', normal_style),
            Paragraph('<b>QTY</b>', normal_style),
            Paragraph('<b>UNIT PRICE (Rs.)</b>', normal_style),
            Paragraph('<b>TAXABLE VALUE (Rs.)</b>', normal_style),
            Paragraph('<b>GST</b>', normal_style),
            Paragraph('<b>TOTAL (Rs.)</b>', normal_style)
        ])

        # Add items with GST breakdown
        for item in order.items.all():
            gst_rate = item.product.get_gst_rate() if item.product else None
            gst_percentage = gst_rate.rate if gst_rate else 18
            hsn_code = gst_rate.hsn_code if gst_rate else '8471'

            # Calculate correct GST breakdown from MRP (item.total_price is GST-inclusive)
            # Use the product's GST calculation method for accuracy
            if item.product:
                gst_breakdown = item.product.calculate_gst_breakdown_from_mrp(quantity=item.quantity)
                taxable_value = gst_breakdown['total_base_price']
                gst_amount = gst_breakdown['total_gst']
                total_with_gst = gst_breakdown['total_mrp']
            else:
                # Fallback calculation if product is not available
                total_with_gst = item.total_price  # This is MRP (GST-inclusive)
                taxable_value = total_with_gst / (1 + Decimal(str(gst_percentage)) / 100)
                gst_amount = total_with_gst - taxable_value

            # Product details with HSN
            product_details = f'<b>{item.product_name}</b><br/>HSN: {hsn_code}'

            # GST breakdown
            gst_details = f'{gst_percentage}%<br/>Rs. {gst_amount:.2f}'

            item_data.append([
                Paragraph(product_details, normal_style),
                Paragraph(f'<b>{item.quantity}</b>', normal_style),
                Paragraph(f'Rs. {item.unit_price:.2f}', normal_style),
                Paragraph(f'Rs. {taxable_value:.2f}', normal_style),
                Paragraph(gst_details, normal_style),
                Paragraph(f'<b>Rs. {total_with_gst:.2f}</b>', normal_style)
            ])

        # Create items table with proper column widths
        items_table = Table(item_data, colWidths=[2.5*inch, 0.6*inch, 0.8*inch, 1*inch, 0.8*inch, 1*inch])
        items_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f7fafc')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#2d3748')),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('TOPPADDING', (0, 0), (-1, 0), 8),

            # Data rows styling
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Product name left aligned
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),  # Other columns center aligned
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f9f9f9')]),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 6),

            # Borders
            ('LINEBELOW', (0, 0), (-1, 0), 2, colors.HexColor('#e2e8f0')),
            ('LINEBELOW', (0, 1), (-1, -1), 0.5, colors.HexColor('#e2e8f0')),
            ('LINEBEFORE', (0, 0), (-1, -1), 0.5, colors.HexColor('#e2e8f0')),
            ('LINEAFTER', (-1, 0), (-1, -1), 0.5, colors.HexColor('#e2e8f0')),
        ]))
        story.append(items_table)
        story.append(Spacer(1, 25))

        # Payment Summary Section
        story.append(Paragraph("PAYMENT SUMMARY", section_heading_style))
        story.append(Spacer(1, 10))

        # Calculate GST amounts
        gst_amount = order.gst_amount or (order.subtotal * Decimal('0.18'))
        cgst_amount = order.cgst_amount or (gst_amount / 2)
        sgst_amount = order.sgst_amount or (gst_amount / 2)
        igst_amount = order.igst_amount or 0

        # Create summary table with right alignment
        summary_data = []

        # Subtotal
        summary_data.append([
            Paragraph('Subtotal (Taxable Value)', normal_style),
            Paragraph(f'Rs. {order.subtotal:.2f}', normal_style)
        ])

        # GST breakdown (remove hardcoded percentages)
        if igst_amount > 0:
            summary_data.append([
                Paragraph('IGST', normal_style),
                Paragraph(f'Rs. {igst_amount:.2f}', normal_style)
            ])
        else:
            summary_data.append([
                Paragraph('CGST', normal_style),
                Paragraph(f'Rs. {cgst_amount:.2f}', normal_style)
            ])
            summary_data.append([
                Paragraph('SGST', normal_style),
                Paragraph(f'Rs. {sgst_amount:.2f}', normal_style)
            ])

        # Shipping if applicable
        if order.shipping_cost > 0:
            summary_data.append([
                Paragraph('Shipping Charges', normal_style),
                Paragraph(f'Rs. {order.shipping_cost:.2f}', normal_style)
            ])

        # Discount if applicable
        if hasattr(order, 'promo_discount') and order.promo_discount > 0:
            summary_data.append([
                Paragraph('Discount', normal_style),
                Paragraph(f'-Rs. {order.promo_discount:.2f}', normal_style)
            ])

        # Total amount
        summary_data.append([
            Paragraph('<b>TOTAL AMOUNT</b>', section_heading_style),
            Paragraph(f'<b>Rs. {order.total:.2f}</b>', section_heading_style)
        ])

        # Create summary table
        summary_table = Table(summary_data, colWidths=[4.5*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            # General styling
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, -2), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -2), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),

            # Total row styling
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f7fafc')),
            ('LINEABOVE', (0, -1), (-1, -1), 2, colors.HexColor('#2d3748')),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#2d3748')),

            # Subtle lines between rows
            ('LINEBELOW', (0, 0), (-1, -2), 0.5, colors.HexColor('#e2e8f0')),
        ]))
        story.append(summary_table)

        # Footer section
        story.append(Spacer(1, 30))

        # Terms and conditions
        story.append(Paragraph("TERMS & CONDITIONS", section_heading_style))
        terms_text = """
        • This is a computer-generated invoice and does not require a signature.<br/>
        • All disputes are subject to Hyderabad jurisdiction only.<br/>
        • Payment terms: Net 30 days from invoice date.<br/>
        • Goods once sold will not be taken back or exchanged.
        """
        story.append(Paragraph(terms_text, small_style))
        story.append(Spacer(1, 20))

        # Thank you message
        story.append(HRFlowable(width="100%", thickness=1, color=colors.HexColor('#e2e8f0')))
        story.append(Spacer(1, 15))

        thank_you_text = f"""
        <para align="center">
        <b>Thank you for shopping with {self.company_details['name']}!</b><br/>
        For any queries, contact us at {self.company_details['email']} or {self.company_details['phone']}<br/>
        Visit us at {self.company_details.get('website', 'https://trio.net.in')}
        </para>
        """
        story.append(Paragraph(thank_you_text, normal_style))

        # Invoice generation timestamp
        story.append(Spacer(1, 10))
        timestamp_text = f"""
        <para align="center">
        <i>Invoice generated on {invoice.generated_at.strftime('%d %B %Y at %I:%M %p')}</i>
        </para>
        """
        story.append(Paragraph(timestamp_text, small_style))

        # Build PDF
        doc.build(story)
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content


# Global instance
invoice_service = InvoiceGenerationService()
